import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_kit/src/shared/locator.dart';
import 'package:flutter_kit/src/shared/services/storage/storage.dart';
import 'package:flutter_kit/src/shared/config/managers/config_manager.dart';

class AppInitializer {
  /// Initialize services, plugins, etc. before the app runs.
  Future<void> preAppRun() async {
    await SystemChrome.setEnabledSystemUIMode(SystemUiMode.edgeToEdge);

    await locator<Storage>().init();

    // 初始化配置管理器（异步，不阻塞启动）
    debugPrint('开始异步初始化配置管理器');
    ConfigManager().initialize().then((_) {
      debugPrint('配置管理器初始化完成');
    }).catchError((e) {
      debugPrint('配置管理器初始化失败: $e');
    });
  }

  /// Initialize services, plugins, etc. after the app runs.
  Future<void> postAppRun() async {
    // Hide RSOD in release mode.
    if (kReleaseMode) {
      ErrorWidget.builder = (FlutterErrorDetails details) => const SizedBox();
    }
  }
}
