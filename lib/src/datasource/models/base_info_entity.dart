import 'package:flutter_kit/generated/json/base/json_field.dart';
import 'package:flutter_kit/generated/json/base_info_entity.g.dart';
import 'dart:convert';
export 'package:flutter_kit/generated/json/base_info_entity.g.dart';

@JsonSerializable()
class BaseInfoEntity {
	@JSONField(name: "Name")
	late String name;
	@JSONField(name: "GenderCode")
	late String genderCode;
	@JSONField(name: "NationCode")
	late String nationCode;
	@J<PERSON>NField(name: "MaritalStatusCode")
	late String maritalStatusCode;
	@J<PERSON><PERSON>ield(name: "Birthday")
	late String birthday;
	@J<PERSON><PERSON>ield(name: "<PERSON><PERSON>")
	late int stature;
	@JSONField(name: "Phone")
	late String phone;
	@J<PERSON><PERSON>ield(name: "Email")
	late String email;
	@JSONField(name: "QQ")
	late String qQ;
	@JSO<PERSON>ield(name: "Address")
	late String address;
	@JSONField(name: "ComputerLevelCode")
	late String computerLevelCode;
	@J<PERSON>NField(name: "EnglishLevelCode")
	late String englishLevelCode;
	@JSONField(name: "LiveAreaId")
	late String liveAreaId;
	@JSONField(name: "LiveAreaName")
	late String liveAreaName;
	@JSONField(name: "LiveAreaCascadeName")
	late String liveAreaCascadeName;
	@JSONField(name: "NativePlaceAreaId")
	late String nativePlaceAreaId;
	@JSONField(name: "NativePlaceAreaName")
	late String nativePlaceAreaName;
	@JSONField(name: "NativePlaceAreaCascadeName")
	late String nativePlaceAreaCascadeName;
	@JSONField(name: "EducationCode")
	late String educationCode;
	@JSONField(name: "WorkingAgeCode")
	late String workingAgeCode;
	@JSONField(name: "GraduateSchool")
	late String graduateSchool;
	@JSONField(name: "MajorIn")
	late String majorIn;
	@JSONField(name: "Labels")
	late String labels;
	@JSONField(name: "Street")
	late String street;
	@JSONField(name: "MapLocation")
	late String mapLocation;
	@JSONField(name: "JobSeekerGroupCode")
	late String jobSeekerGroupCode;

	BaseInfoEntity();

	/// 创建带有默认值的BaseInfoEntity
	factory BaseInfoEntity.empty() {
		final entity = BaseInfoEntity();
		entity.name = '';
		entity.genderCode = '';
		entity.nationCode = '';
		entity.maritalStatusCode = '';
		entity.birthday = '';
		entity.stature = 0;
		entity.phone = '';
		entity.email = '';
		entity.qQ = '';
		entity.address = '';
		entity.computerLevelCode = '';
		entity.englishLevelCode = '';
		entity.liveAreaId = '';
		entity.liveAreaName = '';
		entity.liveAreaCascadeName = '';
		entity.nativePlaceAreaId = '';
		entity.nativePlaceAreaName = '';
		entity.nativePlaceAreaCascadeName = '';
		entity.educationCode = '';
		entity.workingAgeCode = '';
		entity.graduateSchool = '';
		entity.majorIn = '';
		entity.labels = '';
		entity.street = '';
		entity.mapLocation = '';
		entity.jobSeekerGroupCode = '';
		return entity;
	}

	factory BaseInfoEntity.fromJson(Map<String, dynamic> json) => $BaseInfoEntityFromJson(json);

	Map<String, dynamic> toJson() => $BaseInfoEntityToJson(this);

	@override
	String toString() {
		return jsonEncode(this);
	}
}