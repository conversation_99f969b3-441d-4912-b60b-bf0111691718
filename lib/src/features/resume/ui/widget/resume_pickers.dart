import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'selectable_item.dart';
import 'common_picker.dart';
import '../../../../shared/config/managers/config_manager.dart';
import '../../../../shared/config/models/config_models.dart';

/// 简历选择器组件集合
class ResumePickers {

  /// 出生年份选择器
  static Widget birthYearPicker({
    required BuildContext context,
    required String? currentValue,
    required Function(String) onChanged,
  }) {
    return SelectableItem(
      label: '出生年份',
      value: currentValue,
      hint: '请选择',
      hintColor: const Color(0xFFCCCCCC),
      isRequired: true,
      onTap: () async {
        final years = List.generate(50, (index) => '${2024 - index}年');
        final result = await CommonPicker.show(
          context: context,
          title: '选择出生年份',
          options: years,
          currentValue: currentValue,
        );
        if (result != null) {
          onChanged(result);
        }
      },
    );
  }

  /// 求职身份选择器
  static Widget jobStatusPicker({
    required BuildContext context,
    required String? currentValue,
    required Function(String) onChanged,
  }) {
    return SelectableItem(
      label: '求职身份',
      value: currentValue,
      hint: '请选择',
      hintColor: const Color(0xFFCCCCCC),
      isRequired: true,
      onTap: () async {
        const options = [
          '在校-找暑期实习',
          '在校-找全职',
          '离职-随时到岗',
          '在职-月内到岗',
          '在职-考虑机会',
        ];
        final result = await CommonPicker.show(
          context: context,
          title: '选择求职身份',
          options: options,
          currentValue: currentValue,
        );
        if (result != null) {
          onChanged(result);
        }
      },
    );
  }

  /// xml选择器
  static Widget xmlPicker({
    required BuildContext context,
    required String? currentValue,
    required Function(String) onChanged,
    required title,
    required label,
    required isRequired,
    required groundName,
  }) {
    return FutureBuilder<String?>(
      future: ConfigManager().getValueByKey(groundName, currentValue ?? ''),
      builder: (context, snapshot) {
        final displayValue = snapshot.data;

        return SelectableItem(
          label: label,
          value: displayValue,
          hint: '请选择',
          hintColor: const Color(0xFFCCCCCC),
          isRequired: isRequired,
          onTap: () async {
            String? currentDisplayValue = currentValue;
            final convertedValue =
            await ConfigManager().getValueByKey(groundName, currentValue ?? '');
            if (convertedValue != null) {
              currentDisplayValue = convertedValue;
            }

            final result = await CommonPicker.show(
              context: context,
              title: title,
              options: await ConfigManager().getGroupOptionsList(groundName) ?? [],
              currentValue: currentDisplayValue,
            );

            if (result != null) {
              // 将选择的显示值转换为key
              final selectedKey =
              await ConfigManager().getKeyByValue(groundName, result);
              if (selectedKey != null) {
                onChanged(selectedKey);
              }
            }
          },
        );
      },
    );
  }

  /// 姓名输入项
  static Widget nameInput({
    required String? currentValue,
    required Function(String) onChanged,
    required VoidCallback onTap,
  }) {
    return SelectableItem(
      label: '姓名',
      value: currentValue,
      hint: '请输入姓名',
      hintColor: const Color(0xFFCCCCCC),
      isRequired: true,
      onTap: onTap,
    );
  }
}

/// 简历选择器工具类
class ResumePickerUtils {
  /// 生成出生年份选项
  static List<String> generateBirthYears({int startYear = 1950, int? endYear}) {
    final currentYear = endYear ?? DateTime.now().year;
    return List.generate(
      currentYear - startYear + 1,
      (index) => '${currentYear - index}年',
    );
  }

  /// 求职身份选项
  static const List<String> jobStatusOptions = [
    '在校-找暑期实习',
    '在校-找全职',
    '离职-随时到岗',
    '在职-月内到岗',
    '在职-考虑机会',
  ];

}
