import 'package:flutter/material.dart';
import 'package:flutter_kit/src/datasource/models/base_info_entity.dart';
import 'package:flutter_kit/src/features/base_info/logic/base_info_logic.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';

import '../../../resume/theme/resume_theme.dart';
import '../../../resume/ui/widget/resume_pickers.dart';
import '../../../resume/ui/widget/selectable_item.dart';
import '../../../resume/ui/widget/text_edit_dialog.dart';
import 'build_divider.dart';
import 'build_upload_avatar_option.dart';
import 'get_image_provider.dart';

/// 基本信息页面视图组件
///
/// 这是一个纯粹的Widget组件，负责：
/// - 展示基本信息的UI界面
/// - 处理用户交互（编辑、选择等）
/// - 接收Logic传递的数据进行展示
/// - 不处理状态管理和数据加载
class BaseInfoPageView extends StatefulWidget {
  final BaseInfoLogic logic;

  const BaseInfoPageView({
    super.key,
    required this.logic,
  });

  @override
  State<BaseInfoPageView> createState() => _BaseInfoPageViewState();
}

class _BaseInfoPageViewState extends State<BaseInfoPageView> {
  // 本地编辑状态，用于临时保存用户的修改
  late BaseInfoEntity _editingEntity;

  @override
  void initState() {
    super.initState();
    // 初始化编辑实体
    _initEditingEntity();

    // 监听logic数据变化
    widget.logic.addListener(_onLogicDataChanged);
  }

  @override
  void dispose() {
    widget.logic.removeListener(_onLogicDataChanged);
    super.dispose();
  }

  void _initEditingEntity() {
    // 如果logic有数据就使用，否则创建空对象
    _editingEntity =
        widget.logic.baseInfoEntity?.copyWith() ?? BaseInfoEntity.empty();
  }

  void _onLogicDataChanged() {
    // 优化：只在数据真正变化时才调用setState
    if (mounted && widget.logic.baseInfoEntity != null) {
      final newEntity = widget.logic.baseInfoEntity!;
      // 简单的数据比较，避免不必要的重建
      if (_editingEntity.name != newEntity.name ||
          _editingEntity.genderCode != newEntity.genderCode ||
          _editingEntity.birthday != newEntity.birthday) {
        setState(() {
          _editingEntity = newEntity.copyWith();
        });
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    // 使用ListView.builder替代SingleChildScrollView + Column，减少键盘重建开销
    return ListView(
      padding: EdgeInsets.all(16.w),
      // 禁用物理滚动效果，减少性能开销
      physics: const ClampingScrollPhysics(),
      children: [
        RepaintBoundary(
          child: _buildPersonalInfoSection(),
        ),
        SizedBox(height: 16.h),
        // 可以继续添加其他信息区块
        RepaintBoundary(
          child: _buildContactInfoSection(),
        ),
        SizedBox(height: 16.h),
        // 可以补充其他信息
        RepaintBoundary(
          child: _buildOtherInfoSection(),
        ),
      ],
    );
  }

  /// 保存基本信息到服务器
  void _saveBaseInfo() {
    // TODO: 实现保存逻辑，调用logic的保存方法
    // widget.logic.saveBaseInfo(_editingEntity);
  }

  /// 构建个人信息区块
  Widget _buildPersonalInfoSection() {
    return _buildStyledCard(
      title: '个人信息',
      child: Column(
        children: [
          _buildAvatarRow(),
          buildDivider(),
          SelectableItem(
            label: '姓名',
            value: _editingEntity.name,
            hint: '请输入姓名',
            hintColor: const Color(0xFFCCCCCC),
            isRequired: true,
            onTap: () {
              TextEditDialog.show(
                context: context,
                title: '编辑姓名',
                initialText: _editingEntity.name,
                maxLength: 20,
                maxLines: 1,
                hintText: '请输入您的姓名',
                heightRatio: 0.7,
                onSave: (text) {
                  // 优化：只在值真正改变时才调用setState
                  if (_editingEntity.name != text) {
                    setState(() {
                      _editingEntity.name = text;
                    });
                  }
                },
              );
            },
          ),
          buildDivider(),
          ResumePickers.xmlPicker(
            context: context,
            currentValue: _editingEntity.genderCode,
            groundName: 'Gender',
            isRequired: true,
            label: '性别',
            title: '选择性别',
            onChanged: (value) {
              // 优化：只在值真正改变时才调用setState
              if (_editingEntity.genderCode != value) {
                setState(() => _editingEntity.genderCode = value);
              }
            },
          ),
          buildDivider(),
          ResumePickers.birthYearPicker(
            context: context,
            currentValue: _editingEntity.birthday,
            onChanged: (value) {
              // 优化：只在值真正改变时才调用setState
              if (_editingEntity.birthday != value) {
                setState(() => _editingEntity.birthday = value);
                _saveBaseInfo();
              }
            },
          ),
          buildDivider(),
          ResumePickers.xmlPicker(
            context: context,
            currentValue: _editingEntity.maritalStatusCode,
            groundName: 'MaritalStatus',
            isRequired: true,
            label: '婚姻状况',
            title: '选择婚姻状况',
            onChanged: (value) {
              // 优化：只在值真正改变时才调用setState
              if (_editingEntity.maritalStatusCode != value) {
                setState(() => _editingEntity.maritalStatusCode = value);
              }
            },
          ),
          // buildDivider(),
          // ResumePickers.xmlPicker(
          //   context: context,
          //   currentValue: _editingEntity.nationCode,
          //   groundName: 'Nation',
          //   label: '民族',
          //   title: '选择民族',
          //   onChanged: (value) {
          //     // 优化：只在值真正改变时才调用setState
          //     if (_editingEntity.nationCode != value) {
          //       setState(() => _editingEntity.nationCode = value);
          //     }
          //   },
          // ),
          // buildDivider(),
          // ResumePickers.xmlPicker(
          //   context: context,
          //   currentValue: _editingEntity.nationCode,
          //   groundName: 'NationCode',
          //   label: '民族',
          //   title: '选择民族',
          //   onChanged: (value) {
          //     // 优化：只在值真正改变时才调用setState
          //     if (_editingEntity.nationCode != value) {
          //       setState(() => _editingEntity.nationCode = value);
          //     }
          //   },
          // ),
          // buildDivider(),
          // ResumePickers.xmlPicker(
          //   context: context,
          //   currentValue: _editingEntity.nationCode,
          //   groundName: 'NationCode',
          //   label: '民族',
          //   title: '选择民族',
          //   onChanged: (value) {
          //     // 优化：只在值真正改变时才调用setState
          //     if (_editingEntity.nationCode != value) {
          //       setState(() => _editingEntity.nationCode = value);
          //     }
          //   },
          // ),

          buildDivider(),
          ResumePickers.xmlPicker(
            context: context,
            currentValue: _editingEntity.workingAgeCode,
            groundName: 'WorkAge',
            isRequired: true,
            label: '工龄',
            title: '请选择工龄',
            onChanged: (value) {
              // 优化：只在值真正改变时才调用setState
              if (_editingEntity.workingAgeCode != value) {
                setState(() => _editingEntity.workingAgeCode = value);
              }
            },
          ),

          buildDivider(),
          ResumePickers.xmlPicker(
            context: context,
            currentValue: _editingEntity.educationCode,
            groundName: 'Education',
            isRequired: true,
            label: '最高学历',
            title: '请选择最高学历',
            onChanged: (value) {
              // 优化：只在值真正改变时才调用setState
              if (_editingEntity.educationCode != value) {
                setState(() => _editingEntity.educationCode = value);
              }
            },
          ),

          buildDivider(),
          ResumePickers.xmlPicker(
            context: context,
            currentValue: _editingEntity.nationCode,
            groundName: 'Nation',
            isRequired: true,
            label: '现居住地',
            title: '请选择现居住地',
            onChanged: (value) {
              // 优化：只在值真正改变时才调用setState
              if (_editingEntity.nationCode != value) {
                setState(() => _editingEntity.nationCode = value);
              }
            },
          ),

          buildDivider(),
          ResumePickers.xmlPicker(
            context: context,
            currentValue: _editingEntity.labels,
            groundName: 'ResumeLabel',
            isRequired: true,
            label: '个人标签',
            title: '请选择个人标签',
            onChanged: (value) {
              // 优化：只在值真正改变时才调用setState
              if (_editingEntity.labels != value) {
                setState(() => _editingEntity.labels = value);
              }
            },
          ),
        ],
      ),
    );
  }

  /// 构建联系方式区块
  Widget _buildContactInfoSection() {
    return _buildStyledCard(
        title: '联系方式',
        child: Column(
          children: [
            buildDivider(),
            SelectableItem(
              label: '手机号码',
              value: _editingEntity.phone,
              hint: '请输入手机号码',
              hintColor: const Color(0xFFCCCCCC),
              isRequired: true,
              onTap: () {
                TextEditDialog.show(
                  context: context,
                  title: '编辑手机号码',
                  initialText: _editingEntity.phone,
                  maxLength: 20,
                  maxLines: 1,
                  hintText: '请输入您的手机号码',
                  heightRatio: 0.7,
                  onSave: (text) {
                    // 优化：只在值真正改变时才调用setState
                    if (_editingEntity.phone != text) {
                      setState(() {
                        _editingEntity.phone = text;
                      });
                    }
                  },
                );
              },
            ),
            buildDivider(),
            SelectableItem(
              label: '邮箱',
              value: _editingEntity.email,
              hint: '请输入邮箱',
              hintColor: const Color(0xFFCCCCCC),
              isRequired: false,
              onTap: () {
                TextEditDialog.show(
                  context: context,
                  title: '编辑邮箱',
                  initialText: _editingEntity.email,
                  maxLength: 20,
                  maxLines: 1,
                  hintText: '请输入您的邮箱',
                  heightRatio: 0.7,
                  onSave: (text) {
                    // 优化：只在值真正改变时才调用setState
                    if (_editingEntity.email != text) {
                      setState(() {
                        _editingEntity.email = text;
                      });
                    }
                  },
                );
              },
            ),
          ],
        ));
  }

  /// 构建其他信息区块
  Widget _buildOtherInfoSection() {
    return _buildStyledCard(
        title: '其他信息',
        child: Column(
          children: [
            buildDivider(),
            SelectableItem(
              label: '毕业院校',
              value: _editingEntity.graduateSchool,
              hint: '请输入毕业院校',
              hintColor: const Color(0xFFCCCCCC),
              isRequired: false,
              onTap: () {
                TextEditDialog.show(
                  context: context,
                  title: '编辑毕业院校',
                  initialText: _editingEntity.graduateSchool,
                  maxLength: 20,
                  maxLines: 1,
                  hintText: '请输入毕业院校',
                  heightRatio: 0.7,
                  onSave: (text) {
                    // 优化：只在值真正改变时才调用setState
                    if (_editingEntity.graduateSchool != text) {
                      setState(() {
                        _editingEntity.graduateSchool = text;
                      });
                    }
                  },
                );
              },
            ),
            buildDivider(),
            SelectableItem(
              label: '主修专业',
              value: _editingEntity.majorIn,
              hint: '请输入主修专业',
              hintColor: const Color(0xFFCCCCCC),
              isRequired: false,
              onTap: () {
                TextEditDialog.show(
                  context: context,
                  title: '编辑主修专业',
                  initialText: _editingEntity.majorIn,
                  maxLength: 20,
                  maxLines: 1,
                  hintText: '请输入主修专业',
                  heightRatio: 0.7,
                  onSave: (text) {
                    // 优化：只在值真正改变时才调用setState
                    if (_editingEntity.majorIn != text) {
                      setState(() {
                        _editingEntity.majorIn = text;
                      });
                    }
                  },
                );
              },
            ),
            buildDivider(),
            ResumePickers.xmlPicker(
              context: context,
              currentValue: _editingEntity.computerLevelCode,
              groundName: 'ComputerLevel',
              label: '计算机等级',
              title: '请选择计算机等级',
              isRequired: false,
              onChanged: (value) {
                // 优化：只在值真正改变时才调用setState
                if (_editingEntity.computerLevelCode != value) {
                  setState(() => _editingEntity.computerLevelCode = value);
                }
              },
            ),
            buildDivider(),
            ResumePickers.xmlPicker(
              context: context,
              currentValue: _editingEntity.englishLevelCode,
              groundName: 'EnglishLevel',
              isRequired: false,
              label: '英语等级',
              title: '请选择英语等级',
              onChanged: (value) {
                // 优化：只在值真正改变时才调用setState
                if (_editingEntity.englishLevelCode != value) {
                  setState(() => _editingEntity.englishLevelCode = value);
                }
              },
            ),
          ],
        ));
  }

  /// 构建头像行
  Widget _buildAvatarRow() {
    return GestureDetector(
      onTap: () {
        _showAvatarPicker(context);
      },
      behavior: HitTestBehavior.opaque, // 确保整个区域都可以点击
      child: Padding(
        padding: EdgeInsets.symmetric(vertical: 12.h),
        child: Row(
          crossAxisAlignment: CrossAxisAlignment.center, // 垂直居中对齐
          children: [
            Expanded(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    '头像',
                    style: TextStyle(
                      fontSize: 16.sp,
                      color: ResumeTheme.textPrimary,
                      fontWeight: FontWeight.w500,
                    ),
                  ),
                  SizedBox(height: 7.h),
                  Text(
                    '点击修改头像',
                    style: TextStyle(
                      fontSize: 12.sp,
                      color: ResumeTheme.textSecondary,
                    ),
                  ),
                ],
              ),
            ),
            SizedBox(width: 8.w), // 添加间距
            Container(
              width: 56.w,
              height: 56.w,
              decoration: BoxDecoration(
                shape: BoxShape.circle,
                image: DecorationImage(
                  image: getImageProvider(''),
                  fit: BoxFit.cover,
                ),
                border: Border.all(
                  color: ResumeTheme.borderColor,
                  width: 1,
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }

  /// 显示头像选择器
  void _showAvatarPicker(BuildContext context) {
    showModalBottomSheet(
      context: context,
      backgroundColor: Colors.transparent,
      builder: (BuildContext context) {
        return Container(
          decoration: BoxDecoration(
            color: ResumeTheme.surfaceColor,
            borderRadius: BorderRadius.only(
              topLeft: Radius.circular(16.r),
              topRight: Radius.circular(16.r),
            ),
          ),
          child: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              // 头部标题
              Padding(
                padding: EdgeInsets.symmetric(horizontal: 16.w, vertical: 16.h),
                child: Row(
                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  children: [
                    TextButton(
                      onPressed: () => Navigator.pop(context),
                      child: Text(
                        '取消',
                        style: TextStyle(color: ResumeTheme.textSecondary),
                      ),
                    ),
                    Text(
                      '选择头像',
                      style: TextStyle(
                        fontSize: 18.sp,
                        fontWeight: FontWeight.w600,
                        color: ResumeTheme.textPrimary,
                      ),
                    ),
                    SizedBox(width: 48.w), // 占位，保持标题居中
                  ],
                ),
              ),
              Container(height: 1.h, color: ResumeTheme.borderColor),

              // 默认头像选项
              Padding(
                padding: EdgeInsets.all(20.w),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    SizedBox(height: 16.h),
                    Row(
                      mainAxisAlignment: MainAxisAlignment.spaceAround,
                      children: [
                        _buildAvatarOption(
                          'assets/images/ic_head_kt1.png',
                          isAsset: true,
                        ),
                        _buildAvatarOption(
                          'assets/images/ic_head_kt2.png',
                          isAsset: true,
                        ),
                        buildUploadAvatarOption(context),
                      ],
                    ),
                    SizedBox(height: 30.h),
                  ],
                ),
              ),
            ],
          ),
        );
      },
    );
  }

  /// 统一样式的卡片容器
  Widget _buildStyledCard({
    required String title,
    required Widget child,
    String? actionText,
    VoidCallback? onActionTap,
  }) {
    return Container(
      padding: EdgeInsets.symmetric(horizontal: 16.w),
      decoration: BoxDecoration(
        color: ResumeTheme.surfaceColor,
        borderRadius: BorderRadius.circular(16.r),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Padding(
            padding: EdgeInsets.symmetric(vertical: 16.h),
            child: Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Text(
                  title,
                  style: TextStyle(
                    fontSize: 18.sp,
                    fontWeight: FontWeight.w600,
                    color: ResumeTheme.textPrimary,
                  ),
                ),
                if (actionText != null)
                  GestureDetector(
                    onTap: onActionTap,
                    child: Text(
                      actionText,
                      style: TextStyle(
                        color: ResumeTheme.primaryColor,
                        fontSize: 14.sp,
                        fontWeight: FontWeight.w500,
                      ),
                    ),
                  )
              ],
            ),
          ),
          Container(
            height: 1.h,
            color: ResumeTheme.borderColor,
          ),
          child,
        ],
      ),
    );
  }

  /// 构建头像选项
  Widget _buildAvatarOption(String imagePath, {bool isAsset = false}) {
    // final isSelected = _baseInfoEntity.a == imagePath;
    return GestureDetector(
      onTap: () {
        setState(() {
          // _resumeData.avatarUrl = imagePath;
        });
        Navigator.pop(context);
      },
      child: Column(
        children: [
          Container(
            width: 70.w,
            height: 70.w,
            decoration: BoxDecoration(
              shape: BoxShape.circle,
              image: DecorationImage(
                image: isAsset
                    ? AssetImage(imagePath) as ImageProvider
                    : NetworkImage(imagePath),
                fit: BoxFit.cover,
              ),
              border: Border.all(
                  // color: isSelected ? ResumeTheme.primaryColor : ResumeTheme
                  //     .borderColor,
                  // width: isSelected ? 3 : 1,
                  ),
            ),
          ),
        ],
      ),
    );
  }
}
