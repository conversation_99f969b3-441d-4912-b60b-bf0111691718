import 'package:auto_route/auto_route.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';

import '../../../resume/theme/resume_theme.dart';

/// 构建应用栏
Widget customBuildAppBar(BuildContext context,String title, {VoidCallback? onSave}) {
  return Container(
    padding: EdgeInsets.fromLTRB(16.w, 16.h, 16.w, 16.h),
    color: Colors.transparent,
    child: Row(
      children: [
        GestureDetector(
          onTap: () {
            context.router.pop();
          },
          child: Container(
            width: 40.w,
            height: 40.w,
            decoration: BoxDecoration(
              color: ResumeTheme.surfaceColor.withValues(alpha: 0.9),
              shape: BoxShape.circle,
            ),
            child: Icon(
              Icons.arrow_back_ios_new,
              size: 18.w,
              color: ResumeTheme.textPrimary,
            ),
          ),
        ),
        SizedBox(width: 16.w),
        Expanded(
          child: Text(
            title,
            style: TextStyle(
              fontSize: 20.sp,
              fontWeight: FontWeight.w600,
              color: ResumeTheme.textPrimary,
            ),
          ),
        ),
        GestureDetector(
          onTap: () {
            // 先执行保存回调，再返回
            if (onSave != null) {
              onSave();
            }
            context.router.pop();
          },
          child: Container(
            padding: EdgeInsets.symmetric(horizontal: 16.w, vertical: 8.h),
            decoration: BoxDecoration(
              color: ResumeTheme.primaryColor,
              borderRadius: BorderRadius.circular(20.r),
            ),
            child: Text(
              '保存',
              style: TextStyle(
                fontSize: 14.sp,
                fontWeight: FontWeight.w600,
                color: Colors.white,
              ),
            ),
          ),
        ),
      ],
    ),
  );
}