
import 'package:auto_route/auto_route.dart';
import 'package:flutter/material.dart';
import 'package:flutter_kit/src/base/base.dart';
import 'package:flutter_kit/src/features/base_info/logic/base_info_logic.dart';
import 'package:flutter_kit/src/features/base_info/ui/widget/base_info_page_view.dart';
import 'package:flutter_kit/src/base/widgets/app_background.dart';
import 'package:flutter_kit/src/features/base_info/ui/widget/build_app_bar.dart';
import 'package:flutter_kit/src/features/resume/theme/resume_theme.dart';
import 'package:flutter_kit/src/shared/locator.dart';
import 'package:flutter_kit/src/shared/utils/safe_area_manager.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';

/// 基本信息页面
///
/// 功能特性：
/// - 使用ViewStateWidget进行状态管理
/// - 自动处理Loading/Success/Error状态
/// - 集成BaseInfoPageView组件
/// - 支持数据自动加载和刷新
@RoutePage()
class BaseInfoScreen extends ViewStateWidget<BaseInfoLogic> {
  const BaseInfoScreen({super.key});

  @override
  BaseInfoLogic createController() {
    return locator<BaseInfoLogic>();
  }

  @override
  PreferredSizeWidget? buildAppBar(BuildContext context, BaseInfoLogic logic) {
    // 不使用默认AppBar，在buildBody中使用自定义AppBar
    return null;
  }

  @override
  Widget buildBody(BuildContext context, BaseInfoLogic logic) {
    // 移除Scaffold，直接返回内容，让ViewStateWidget处理Scaffold
    return AppBackground(
      keyboardOptimized: true, // 启用键盘优化
      child: Column(
        children: [
          // 使用缓存的安全区域值，避免MediaQuery重复调用
          SizedBox(height: SafeAreaManager.instance.isInitialized
              ? SafeAreaManager.instance.topPadding
              : MediaQuery.of(context).padding.top),
          // 自定义AppBar，传递保存回调
          customBuildAppBar(
            context,
            '基本信息',
            onSave: () {
              // TODO: 实现保存逻辑
              // logic.saveBaseInfo();
            },
          ),
          // 页面内容 - 直接使用Expanded，让BaseInfoPageView处理滚动
          Expanded(
            child: BaseInfoPageView(logic: logic),
          ),
        ],
      ),
    );
  }

  @override
  bool hasData() {
    // 检查是否有数据，用于决定Loading状态的显示方式
    final logic = createController();
    return logic.baseInfoEntity != null;
  }

  @override
  bool resizeToAvoidBottomInset() {
    // 启用键盘避让，但不会导致重建问题（因为我们优化了布局）
    return true;
  }

  @override
  Duration getTransitionDuration() {
    // 减少状态切换动画时间，提升响应速度
    return const Duration(milliseconds: 150);
  }
}